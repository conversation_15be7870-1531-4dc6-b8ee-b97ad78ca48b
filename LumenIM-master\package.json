{"name": "lumenim", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite --mode development", "build": "vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@highlightjs/vue-plugin": "^2.1.0", "@vueuse/core": "^13.1.0", "axios": "^1.8.4", "core-js": "^3.39.0", "dayjs": "^1.11.13", "highlight.js": "^11.5.0", "js-audio-recorder": "^1.0.7", "jsencrypt": "^3.3.2", "lru-cache": "^11.0.2", "md-editor-v3": "^5.6.0", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.2.0", "quill": "^2.0.3", "quill-mention": "^6.1.1", "uuid": "^11.1.0", "vue": "^3.5.14", "vue-cropper": "^1.1.3", "vue-draggable-plus": "^0.6.0", "vue-router": "^4.5.1", "xgplayer": "^3.0.21"}, "devDependencies": {"@icon-park/vue-next": "^1.4.2", "@rushstack/eslint-patch": "^1.3.3", "@tsconfig/node18": "^18.2.2", "@types/node": "^20.11.1", "@types/sortablejs": "^1.15.0", "@vitejs/plugin-vue": "^5.2.4", "@vitejs/plugin-vue-jsx": "^4.2.0", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/tsconfig": "^0.7.0", "cross-env": "^7.0.3", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-vue": "^10.1.0", "less": "^4.3.0", "less-loader": "^12.2.0", "naive-ui": "^2.41.0", "prettier": "^3.5.3", "typescript": "~5.2.0", "unplugin-auto-import": "^19.1.2", "unplugin-vue-components": "^28.5.0", "vite": "^6.3.5", "vite-plugin-compression": "^0.5.1", "vue-eslint-parser": "^10.0.0", "vue-tsc": "^2.2.8"}}