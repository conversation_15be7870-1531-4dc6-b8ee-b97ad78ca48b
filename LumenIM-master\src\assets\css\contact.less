.title {
  height: 60px;
  line-height: 60px;
  padding-left: 15px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 20px;
  font-weight: 500;
  border-bottom: 1px solid var(--border-color);
}

.view-box {
  padding: 15px;
  padding-top: 0;
  margin-top: 15px;

  .view-list {
    height: 60px;
    margin: 5px 0;
    padding: 5px;
    display: flex;
    padding-left: 0;
    border: 1px solid transparent;
    transition: padding 0.5s ease-in-out;

    &:hover,
    &.selectd {
      border-radius: 2px;
      padding: 5px 10px;
      border: 1px solid rgb(80 138 254);
      cursor: pointer;
    }

    &:first-child {
      margin-top: 0;
    }

    .image {
      width: 60px;
      margin-right: 5px;
      justify-content: flex-start;
    }

    .content {
      flex: auto;

      .name {
        color: rgba(0, 0, 0, 0.65);
        font-size: 15px;
        height: 30px;
        line-height: 30px;
        font-weight: 500;
      }

      .desc {
        height: 30px;
        line-height: 30px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 14px;
      }
    }

    .tools {
      width: 100px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }
  }
}
