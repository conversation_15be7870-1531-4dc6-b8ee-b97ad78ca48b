.title {
  height: 60px;
  line-height: 60px;
  padding-left: 15px;
  color: var(--im-text-color);
  font-size: 20px;
  font-weight: 500;
  border-bottom: 1px solid var(--border-color);
}

.view-box {
  padding: 15px;
  padding-top: 0;

  .view-list {
    height: 70px;
    margin: 5px 0;
    border-bottom: 1px solid var(--border-color);
    padding: 5px;
    display: flex;
    padding-left: 0;

    &:first-child {
      margin-top: 0;
    }

    .image {
      width: 80px;
      margin-right: 5px;
    }

    .content {
      flex: auto;
      color: var(--im-text-color);

      .name {
        font-size: 15px;
        height: 40px;
        line-height: 40px;
        font-weight: 500;
      }

      .desc {
        height: 30px;
        line-height: 30px;
        font-size: 14px;
        color: #989898;
      }
    }

    .tools {
      min-width: 120px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }
  }
}
