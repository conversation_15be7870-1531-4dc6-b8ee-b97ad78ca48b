/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ApplyTab: typeof import('./src/components/group/manage/ApplyTab.vue')['default']
    AudioMessage: typeof import('./src/components/mechat/component/AudioMessage.vue')['default']
    Avatar: typeof import('./src/components/basic/Avatar.vue')['default']
    AvatarCropper: typeof import('./src/components/basic/AvatarCropper.vue')['default']
    ChatForwardRecord: typeof import('./src/components/mechat/ChatForwardRecord.vue')['default']
    ChatItem: typeof import('./src/components/chat/ChatItem.vue')['default']
    ChatPlus: typeof import('./src/components/chat/ChatPlus.vue')['default']
    CodeMessage: typeof import('./src/components/mechat/component/CodeMessage.vue')['default']
    ConfigTab: typeof import('./src/components/group/manage/ConfigTab.vue')['default']
    ContactModal: typeof import('./src/components/user/ContactModal.vue')['default']
    DetailTab: typeof import('./src/components/group/manage/DetailTab.vue')['default']
    DraggableArea: typeof import('./src/components/basic/DraggableArea.vue')['default']
    Dropdown: typeof import('./src/components/basic/Dropdown.vue')['default']
    Editor: typeof import('./src/components/editor/Editor.vue')['default']
    EditorEmail: typeof import('./src/components/user/EditorEmail.vue')['default']
    EditorMobile: typeof import('./src/components/user/EditorMobile.vue')['default']
    EditorPassword: typeof import('./src/components/user/EditorPassword.vue')['default']
    FileMessage: typeof import('./src/components/mechat/component/FileMessage.vue')['default']
    FooterToolbar: typeof import('./src/components/chat/FooterToolbar.vue')['default']
    ForwardMessage: typeof import('./src/components/mechat/component/ForwardMessage.vue')['default']
    ForwardRecord: typeof import('./src/components/mechat/component/components/ForwardRecord.vue')['default']
    GroupApply: typeof import('./src/components/group/GroupApply.vue')['default']
    GroupLaunch: typeof import('./src/components/group/GroupLaunch.vue')['default']
    GroupNoticeDrawer: typeof import('./src/components/group/GroupNoticeDrawer.vue')['default']
    GroupNoticeMessage: typeof import('./src/components/mechat/component/GroupNoticeMessage.vue')['default']
    GroupPanel: typeof import('./src/components/group/GroupPanel.vue')['default']
    HeaderToolbar: typeof import('./src/components/chat/HeaderToolbar.vue')['default']
    HistoryRecord: typeof import('./src/components/mechat/HistoryRecord.vue')['default']
    ImageMessage: typeof import('./src/components/mechat/component/ImageMessage.vue')['default']
    Loading: typeof import('./src/components/basic/Loading.vue')['default']
    LoginMessage: typeof import('./src/components/mechat/component/LoginMessage.vue')['default']
    Manage: typeof import('./src/components/group/manage/index.vue')['default']
    MeEditorCode: typeof import('./src/components/editor/MeEditorCode.vue')['default']
    MeEditorEmoticon: typeof import('./src/components/editor/MeEditorEmoticon.vue')['default']
    MeEditorLocation: typeof import('./src/components/editor/MeEditorLocation.vue')['default']
    MeEditorRecorder: typeof import('./src/components/editor/MeEditorRecorder.vue')['default']
    MeEditorVote: typeof import('./src/components/editor/MeEditorVote.vue')['default']
    MemberDrawer: typeof import('./src/components/group/MemberDrawer.vue')['default']
    MemberTab: typeof import('./src/components/group/manage/MemberTab.vue')['default']
    MixedMessage: typeof import('./src/components/mechat/component/MixedMessage.vue')['default']
    NAlert: typeof import('naive-ui')['NAlert']
    NAvatar: typeof import('naive-ui')['NAvatar']
    NBreadcrumb: typeof import('naive-ui')['NBreadcrumb']
    NBreadcrumbItem: typeof import('naive-ui')['NBreadcrumbItem']
    NButton: typeof import('naive-ui')['NButton']
    NCard: typeof import('naive-ui')['NCard']
    NCheckbox: typeof import('naive-ui')['NCheckbox']
    NCode: typeof import('naive-ui')['NCode']
    NConfigProvider: typeof import('naive-ui')['NConfigProvider']
    NDatePicker: typeof import('naive-ui')['NDatePicker']
    NDialogProvider: typeof import('naive-ui')['NDialogProvider']
    NDivider: typeof import('naive-ui')['NDivider']
    NDrawer: typeof import('naive-ui')['NDrawer']
    NDrawerContent: typeof import('naive-ui')['NDrawerContent']
    NDropdown: typeof import('naive-ui')['NDropdown']
    NEmpty: typeof import('naive-ui')['NEmpty']
    NFloatButton: typeof import('naive-ui')['NFloatButton']
    NForm: typeof import('naive-ui')['NForm']
    NFormItem: typeof import('naive-ui')['NFormItem']
    NIcon: typeof import('naive-ui')['NIcon']
    NImage: typeof import('naive-ui')['NImage']
    NInput: typeof import('naive-ui')['NInput']
    NLayoutContent: typeof import('naive-ui')['NLayoutContent']
    NMessageProvider: typeof import('naive-ui')['NMessageProvider']
    NModal: typeof import('naive-ui')['NModal']
    NModalProvider: typeof import('naive-ui')['NModalProvider']
    NNotificationProvider: typeof import('naive-ui')['NNotificationProvider']
    NoticeEditor: typeof import('./src/components/group/manage/NoticeEditor.vue')['default']
    NoticeTab: typeof import('./src/components/group/manage/NoticeTab.vue')['default']
    NPopconfirm: typeof import('naive-ui')['NPopconfirm']
    NPopover: typeof import('naive-ui')['NPopover']
    NPopselect: typeof import('naive-ui')['NPopselect']
    NProgress: typeof import('naive-ui')['NProgress']
    NRadio: typeof import('naive-ui')['NRadio']
    NRadioGroup: typeof import('naive-ui')['NRadioGroup']
    NScrollbar: typeof import('naive-ui')['NScrollbar']
    NSkeleton: typeof import('naive-ui')['NSkeleton']
    NSpace: typeof import('naive-ui')['NSpace']
    NSwitch: typeof import('naive-ui')['NSwitch']
    NTab: typeof import('naive-ui')['NTab']
    NTabPane: typeof import('naive-ui')['NTabPane']
    NTabs: typeof import('naive-ui')['NTabs']
    NTag: typeof import('naive-ui')['NTag']
    NTooltip: typeof import('naive-ui')['NTooltip']
    NTransfer: typeof import('naive-ui')['NTransfer']
    NTree: typeof import('naive-ui')['NTree']
    NVirtualList: typeof import('naive-ui')['NVirtualList']
    QuillEditor: typeof import('./src/components/quill-editor/QuillEditor.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SkipBottomButton: typeof import('./src/components/chat/SkipBottomButton.vue')['default']
    SysTextMessage: typeof import('./src/components/mechat/component/SysTextMessage.vue')['default']
    SysTextTplMessage: typeof import('./src/components/mechat/component/SysTextTplMessage.vue')['default']
    TextMessage: typeof import('./src/components/mechat/component/TextMessage.vue')['default']
    TreeMenu: typeof import('./src/components/basic/tree-menu/TreeMenu.vue')['default']
    UnknownMessage: typeof import('./src/components/mechat/component/UnknownMessage.vue')['default']
    UploadsModal: typeof import('./src/components/basic/UploadsModal.vue')['default']
    UserCardModal: typeof import('./src/components/user/UserCardModal.vue')['default']
    UserSearchModal: typeof import('./src/components/user/UserSearchModal.vue')['default']
    VideoMessage: typeof import('./src/components/mechat/component/VideoMessage.vue')['default']
    VoteMessage: typeof import('./src/components/mechat/component/VoteMessage.vue')['default']
    Xtime: typeof import('./src/components/basic/Xtime.vue')['default']
  }
}
