#logo-name {
  position: fixed;
  width: 200px;
  height: 38px;
  font-size: 34px;
  font-family:
    Times New Roman,
    Georgia,
    Serif;
  color: #2196f3;
  top: 20px;
  left: 50px;
}

.copyright {
  position: absolute;
  bottom: 30px;
  left: 0;
  right: 0;
  width: 70%;
  text-align: center;
  margin: 0 auto;
  font-size: 12px;
  color: #b1a0a0;

  span {
    margin: 0 5px;
  }

  a {
    color: #777272;
    font-weight: 400;
  }
}

.login-box {
  position: fixed;
  width: 350px;
  min-height: 450px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 10px;
  border: 1px solid #e8e8e8;
  padding: 10px 20px;

  &.forget,
  &.login {
    height: 450px;
  }
  &.reister {
    height: 485px;
  }

  .box-header {
    height: 38px;
    font-size: 22px;
    line-height: 38px;
    margin: 20px 0;
    display: flex;
  }

  .helper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 38px;
    margin-top: 15px;
  }

  .preview-account {
    padding: 8px 0;
    text-align: center;

    p {
      height: 25px;
      line-height: 25px;
      font-weight: 100;
      font-size: 12px;
      cursor: pointer;
    }
  }
}

@media screen and (max-height: 500px) {
  .copyright {
    display: none;
  }
}

.fly-box {
  .fly {
    pointer-events: none;
    position: fixed;
    z-index: 100;
  }

  .bg-fly-circle1 {
    left: 40px;
    top: 100px;
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: linear-gradient(
      to right,
      rgba(100, 84, 239, 0.07) 0%,
      rgba(48, 33, 236, 0.04) 100%
    );
    animation: move 2.5s linear infinite;
  }

  .bg-fly-circle2 {
    left: 3%;
    top: 60%;
    width: 150px;
    height: 150px;
    border-radius: 50%;
    background: linear-gradient(
      to right,
      rgba(100, 84, 239, 0.08) 0%,
      rgba(48, 33, 236, 0.04) 100%
    );
    animation: move 3s linear infinite;
  }

  .bg-fly-circle3 {
    right: 2%;
    top: 140px;
    width: 145px;
    height: 145px;
    border-radius: 50%;
    background: linear-gradient(to right, rgba(100, 84, 239, 0.1) 0%, rgba(48, 33, 236, 0.04) 100%);
    animation: move 2.5s linear infinite;
  }

  .bg-fly-circle4 {
    right: 5%;
    top: 60%;
    width: 160px;
    height: 160px;
    border-radius: 50%;
    background: linear-gradient(
      to right,
      rgba(100, 84, 239, 0.02) 0%,
      rgba(48, 33, 236, 0.04) 100%
    );
    animation: move 3.5s linear infinite;
  }
}

@keyframes move {
  0% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(25px);
  }

  100% {
    transform: translateY(0px);
  }
}

html[theme-mode='dark'] {
  .login-box {
    border: 1px solid #393939;
  }
}
